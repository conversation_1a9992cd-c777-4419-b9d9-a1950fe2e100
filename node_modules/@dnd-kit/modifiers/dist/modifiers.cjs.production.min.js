"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var t=require("@dnd-kit/utilities");function e(t,e,r){const o={...t};return e.top+t.y<=r.top?o.y=r.top-e.top:e.bottom+t.y>=r.top+r.height&&(o.y=r.top+r.height-e.bottom),e.left+t.x<=r.left?o.x=r.left-e.left:e.right+t.x>=r.left+r.width&&(o.x=r.left+r.width-e.right),o}exports.createSnapModifier=function(t){return e=>{let{transform:r}=e;return{...r,x:Math.ceil(r.x/t)*t,y:Math.ceil(r.y/t)*t}}},exports.restrictToFirstScrollableAncestor=t=>{let{draggingNodeRect:r,transform:o,scrollableAncestorRects:n}=t;const i=n[0];return r&&i?e(o,r,i):o},exports.restrictToHorizontalAxis=t=>{let{transform:e}=t;return{...e,y:0}},exports.restrictToParentElement=t=>{let{containerNodeRect:r,draggingNodeRect:o,transform:n}=t;return o&&r?e(n,o,r):n},exports.restrictToVerticalAxis=t=>{let{transform:e}=t;return{...e,x:0}},exports.restrictToWindowEdges=t=>{let{transform:r,draggingNodeRect:o,windowRect:n}=t;return o&&n?e(r,o,n):r},exports.snapCenterToCursor=e=>{let{activatorEvent:r,draggingNodeRect:o,transform:n}=e;if(o&&r){const e=t.getEventCoordinates(r);if(!e)return n;const i=e.x-o.left,s=e.y-o.top;return{...n,x:n.x+i-o.width/2,y:n.y+s-o.height/2}}return n};
//# sourceMappingURL=modifiers.cjs.production.min.js.map
