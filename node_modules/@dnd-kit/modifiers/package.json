{"name": "@dnd-kit/modifiers", "version": "9.0.0", "description": "Translate modifier presets for use with `@dnd-kit` packages.", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/clauderic/dnd-kit.git", "directory": "packages/modifiers"}, "scripts": {"start": "tsdx watch --tsconfig tsconfig.build.json --verbose --noClean", "build": "tsdx build --tsconfig tsconfig.build.json", "test": "tsdx test", "lint": "tsdx lint", "prepublish": "npm run build"}, "main": "dist/index.js", "module": "dist/modifiers.esm.js", "typings": "dist/index.d.ts", "files": ["README.md", "CHANGELOG.md", "LICENSE", "dist"], "dependencies": {"@dnd-kit/utilities": "^3.2.2", "tslib": "^2.0.0"}, "peerDependencies": {"@dnd-kit/core": "^6.3.0", "react": ">=16.8.0"}, "devDependencies": {"@dnd-kit/core": "^6.3.0"}, "publishConfig": {"access": "public"}}