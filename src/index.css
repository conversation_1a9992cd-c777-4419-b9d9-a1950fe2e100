:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color: #212529;
  background-color: #f8f9fa;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

#root {
  min-height: 100vh;
}

a {
  font-weight: 500;
  color: #667eea;
  text-decoration: none;
}

a:hover {
  color: #5a6fd8;
  text-decoration: underline;
}

h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 600;
  line-height: 1.2;
}

p {
  margin: 0;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #667eea;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

button:hover {
  background-color: #5a6fd8;
  transform: translateY(-1px);
}

button:focus,
button:focus-visible {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

button:active {
  transform: translateY(0);
}
