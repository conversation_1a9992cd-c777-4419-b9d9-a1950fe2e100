.user-story-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 1rem;
  cursor: grab;
  transition: all 0.2s ease;
  user-select: none;
}

.user-story-item:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.user-story-item.dragging {
  cursor: grabbing;
  transform: rotate(2deg);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.drag-handle {
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem;
  margin-top: 0.25rem;
}

.user-story-item:hover .drag-handle {
  color: #667eea;
}

.story-position {
  font-weight: 700;
  font-size: 1.2rem;
  color: #667eea;
  min-width: 2.5rem;
  text-align: center;
  margin-top: 0.25rem;
}

.story-content {
  flex: 1;
}

.story-title {
  font-weight: 600;
  font-size: 1.2rem;
  color: #212529;
  margin-bottom: 0.5rem;
}

.story-description {
  font-size: 1rem;
  color: #495057;
  line-height: 1.4;
  margin-bottom: 1rem;
  font-style: italic;
}

.acceptance-criteria {
  font-size: 0.9rem;
}

.acceptance-criteria strong {
  color: #495057;
  display: block;
  margin-bottom: 0.5rem;
}

.acceptance-criteria ul {
  margin: 0;
  padding-left: 1.2rem;
  color: #6c757d;
}

.acceptance-criteria li {
  margin-bottom: 0.25rem;
  line-height: 1.3;
}

.acceptance-criteria li:last-child {
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .user-story-item {
    padding: 1rem;
    gap: 0.75rem;
  }
  
  .story-title {
    font-size: 1.1rem;
  }
  
  .story-description {
    font-size: 0.9rem;
  }
  
  .acceptance-criteria {
    font-size: 0.85rem;
  }
}
