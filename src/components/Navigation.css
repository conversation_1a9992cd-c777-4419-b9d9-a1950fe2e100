.navigation {
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 1.5rem 1rem;
  margin-top: auto;
}

.nav-content {
  max-width: 800px;
  margin: 0 auto;
}

.nav-header {
  margin-bottom: 2rem;
}

.nav-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #495057;
}

.progress-summary {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.progress-stats {
  display: flex;
  align-items: baseline;
  gap: 5px;
  margin-bottom: 15px;
  justify-content: center;
}

.completed-count {
  font-size: 2rem;
  font-weight: bold;
  color: #28a745;
}

.total-count {
  font-size: 1.5rem;
  color: #6c757d;
}

.progress-label {
  font-size: 0.9rem;
  color: #6c757d;
  margin-left: 10px;
}

.progress-bar-container {
  display: flex;
  align-items: center;
  gap: 15px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #28a745, #20c997);
  transition: width 0.5s ease;
}

.progress-percentage {
  font-weight: 600;
  color: #28a745;
  font-size: 0.9rem;
  min-width: 35px;
}

.completion-badge {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  padding: 15px 20px;
  border-radius: 10px;
  text-align: center;
  font-weight: 600;
  margin-top: 15px;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.exercise-list {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.exercise-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  border: 2px solid transparent;
  background: white;
  transition: all 0.2s ease;
  flex: 1;
  min-width: 200px;
}

.exercise-item.clickable {
  cursor: pointer;
}

.exercise-item.clickable:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.exercise-item.active {
  border-color: #667eea;
  background: #f8f9ff;
}

.exercise-item.completed {
  border-color: #28a745;
  background: #f8fff9;
}

.exercise-item.locked {
  opacity: 0.5;
  cursor: not-allowed;
}

.exercise-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.exercise-item.active .exercise-number {
  background: #667eea;
  color: white;
}

.exercise-item.completed .exercise-number {
  background: #28a745;
  color: white;
}

.exercise-item.locked .exercise-number {
  background: #e9ecef;
  color: #6c757d;
}

.exercise-item:not(.active):not(.completed):not(.locked) .exercise-number {
  background: #e9ecef;
  color: #495057;
}

.exercise-info {
  flex: 1;
}

.exercise-title {
  font-weight: 600;
  color: #212529;
  margin-bottom: 0.25rem;
}

.exercise-description {
  font-size: 0.85rem;
  color: #6c757d;
  line-height: 1.3;
  margin-bottom: 0.5rem;
}

.exercise-status {
  margin-top: 0.5rem;
}

.status-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.completed {
  background: #d4edda;
  color: #155724;
}

.status-badge.active {
  background: #cce7ff;
  color: #004085;
}

.status-badge.available {
  background: #fff3cd;
  color: #856404;
}

.status-badge.locked {
  background: #f8f9fa;
  color: #6c757d;
}

@media (max-width: 768px) {
  .exercise-list {
    flex-direction: column;
  }
  
  .exercise-item {
    min-width: auto;
  }
}
