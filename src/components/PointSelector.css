.point-selector {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  min-width: 200px;
}

.point-selector-label {
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
  margin: 0;
}

.point-cards {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  justify-content: center;
}

.point-card {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: white;
  color: #495057;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.point-card:hover {
  border-color: #667eea;
  background: #f8f9ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.point-card:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.point-card:active {
  transform: translateY(0);
}

.point-card.selected {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.point-card.selected:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.point-card.selected:focus {
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3), 0 0 0 3px rgba(102, 126, 234, 0.2);
}

/* Responsive design */
@media (max-width: 768px) {
  .point-selector {
    min-width: 180px;
  }
  
  .point-cards {
    gap: 0.4rem;
  }
  
  .point-card {
    width: 2.2rem;
    height: 2.2rem;
    font-size: 0.9rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .point-card {
    border-width: 3px;
  }
  
  .point-card.selected {
    background: #000;
    border-color: #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .point-card {
    transition: none;
  }
  
  .point-card:hover {
    transform: none;
  }
  
  .point-card.selected:hover {
    transform: none;
  }
}
